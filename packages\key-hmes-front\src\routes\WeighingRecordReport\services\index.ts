import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import axios from 'axios';
import ONotification from '../components/ONotification';

const tenantId = getCurrentOrganizationId();

export const fetchAndGeneratePrintTemplate = async (
  url: string,
  params: any,
): Promise<string | null> => {
  try {
    const res = await axios({
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/${url}`,
      method: 'GET',
      params,
    });

    if (res && !res?.failed) {
      return res[0]?.rollUpTime;
    }

    ONotification.warning({
      message: `${res?.message}`,
    });

    return null;
  } catch (error) {
    ONotification.warning({
      message: `${error.message}`,
    });
    return null;
  }
};