/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-01-16 16:00:00
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-09-25 09:23:13
 * @FilePath: \dst-front\packages\key-hmes-front\src\routes\WeighingRecordReport\components\HandoverRecordTable.tsx
 * @Description: 交接记录表
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Table, DataSet, Button, Modal, Form, Output, NumberField } from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { handoverRecordDS, handoverAdjustDS } from '../stores';
import styles from '../index.module.less';

const tenantId = getCurrentOrganizationId();

const HandoverRecordTable = ({ handoverRecordDs, queryParams }) => {
  const adjustDs = useMemo(() => new DataSet(handoverAdjustDS()), []);
  const [selectedRow, setSelectedRow] = useState<any>(null);

  const getAllRecords = () => handoverRecordDs.records.filter(r => !r.get('isSummary'));

  const round = (value, decimals = 2) => {
    if (value === null || value === undefined || Number.isNaN(Number(value))) return 0;
    const factor = 10 ** decimals;
    return Math.round(Number(value) * factor) / factor;
  };

  const handleHandoverMetersChange = (meters, originalRecord) => {
    const width =
      adjustDs.current?.get('materialWidth') || originalRecord?.get('materialWidth') || 0;
    const prodTargetValue =
      adjustDs.current?.get('prodTargetValue') || originalRecord?.get('prodTargetValue') || 0;
    const weight = round(Number(meters) * Number(width) * 1.07 * Number(prodTargetValue) * 1000, 2);

    adjustDs.current?.set('newTransitionMeter', meters);
    adjustDs.current?.set('newTransitionWight', weight);
  };

  const columns = [
    {
      name: 'recordNo',
      align: ColumnAlign.center,
      width: 110,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
        }
        return record?.get('recordNo');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'prodLineCode',
      align: ColumnAlign.center,
      width: 80,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          // 统计销售卷号有值的记录数量
          const allRecs = getAllRecords();
          const validSalesRollCount = allRecs.filter(
            rec => rec.get('identification') && rec.get('identification').trim() !== '',
          ).length;
          return (
            <span style={{ color: '#000', fontWeight: 'bold' }}>
              本班交接小计（{validSalesRollCount}卷）
            </span>
          );
        }
        return record?.get('prodLineCode');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 9,
          };
        }
        return {};
      },
    },
    {
      name: 'shiftDate',
      align: ColumnAlign.center,
      width: 120,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('shiftDate');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'shiftCode',
      align: ColumnAlign.center,
      width: 120,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('shiftCode');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'shiftTeamName',
      align: ColumnAlign.center,
      width: 100,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('shiftTeamName');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'materialName',
      align: ColumnAlign.center,
      width: 250,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('materialName');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'identification',
      align: ColumnAlign.center,
      width: 150,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('identification');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'identificationProduction',
      align: ColumnAlign.center,
      width: 150,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('identificationProduction');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'rollUpTime',
      align: ColumnAlign.center,
      width: 150,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('rollUpTime');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 0,
            hidden: true,
          };
        }
        return {};
      },
    },
    {
      name: 'transitionMeter',
      align: ColumnAlign.center,
      width: 100,
      renderer: props => {
        const { record } = props as any;
        if (record?.get('isSummary')) {
          const allRecs = getAllRecords();
          const totalMeters = allRecs.reduce(
            (sum, rec) => sum + (Number(rec.get('transitionMeter')) || 0),
            0,
          );
          return (
            <span style={{ color: '#000', fontWeight: 'bold' }}>{totalMeters.toFixed(2)}</span>
          );
        }
        return record?.get('transitionMeter');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 1,
          };
        }
        return {};
      },
    },
    {
      name: 'transitionWight',
      align: ColumnAlign.center,
      width: 100,
      renderer: props => {
        const { record } = props as any;
        if (record?.get('isSummary')) {
          const allRecs = getAllRecords();
          const totalWeight = allRecs.reduce(
            (sum, rec) => sum + (Number(rec.get('transitionWight')) || 0),
            0,
          );
          return (
            <span style={{ color: '#000', fontWeight: 'bold' }}>{totalWeight.toFixed(2)}</span>
          );
        }
        return record?.get('transitionWight');
      },
      onCell: ({ record }) => {
        if (record?.get('isSummary')) {
          return {
            colSpan: 1,
          };
        }
        return {};
      },
    },
    {
      name: 'selfTest1Name',
      align: ColumnAlign.center,
      width: 100,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('selfTest1Name');
      },
    },
    {
      name: 'selfTest2Name',
      align: ColumnAlign.center,
      width: 100,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('selfTest2Name');
      },
    },
    {
      name: 'isolationMembraneTestName',
      align: ColumnAlign.center,
      width: 120,
      renderer: props => {
        const { record } = props;
        if (record?.get('isSummary')) {
          return null;
        }
        return record?.get('isolationMembraneTestName');
      },
    },
  ];

  const handleAdjustClick = () => {
    const data = selectedRow.toData();
    const adjustFromData = {
      ...data,
      newTransitionWight: data.transitionWight,
      newTransitionMeter: data.transitionMeter,
    };

    adjustDs.loadData([adjustFromData]);
    Modal.open({
      destroyOnClose: true,
      title: '交接卷调整',
      style: { width: 800 },
      children: (
        <Form dataSet={adjustDs} columns={2}>
          <Output name="identification" label="销售卷号" />
          <Output name="identificationProduction" label="生产卷号" />
          <Output name="shiftTeamName" label="班组" />
          <Output name="shiftCode" label="班次" />
          <Output name="shiftDate" label="日期" />
          <Output name="materialName" label="物料名称" />
          <Output name="rollUpTime" label="上卷时间" />
          <NumberField
            name="newTransitionMeter"
            label="交接米数"
            min={0}
            step={1}
            onChange={value => handleHandoverMetersChange(value, adjustFromData)}
          />
          <Output name="newTransitionWight" label="交接重量" />
        </Form>
      ),
      onOk: async () => {
        const adjustData = adjustDs.toData()?.[0] || {};
        const { shiftTeamActualId, prodLineId, eoId, workOrderId } =
          adjustFromData?.toData?.() || {};
        const payload = {
          ...originalData,
          ...adjustData,
        };

        console.log('payload', payload);

        const res = await request(
          `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/handover-volume/adjustment/ui`,
          {
            method: 'POST',
            body: payload,
          },
        );
        if (res && res.success) {
          // 重新查询数据以刷新表格
          handoverRecordDs.query().then(queryRes => {
            if (queryRes && queryRes?.success && queryRes?.rows) {
              const dataWithNo = queryRes?.rows?.map((item, index) => ({
                ...item,
                recordNo: index + 1,
              }));
              // 添加汇总行
              const dataWithSummary = [
                ...dataWithNo,
                {
                  recordNo: '',
                  prodLineCode: '',
                  shiftDate: '',
                  shiftCode: '',
                  shiftTeamName: '',
                  materialName: '',
                  identification: '',
                  identificationProduction: '',
                  rollUpTime: '',
                  transitionMeter: '',
                  transitionWight: '',
                  selfTest1Name: '',
                  selfTest2Name: '',
                  isolationMembraneTestName: '',
                  isSummary: true,
                },
              ];
              handoverRecordDs.loadData(dataWithSummary);
            }
          });
          return true;
        }
        return false;
      },
    });
  };

  useEffect(() => {
    if (queryParams && Object.keys(queryParams).length > 0) {
      const {
        dateFrom,
        dateTo,
        prodLineId,
        shiftTeamId,
        shiftTeamName,
        shiftCode,
        materialName,
        customerName,
      } = queryParams;
      handoverRecordDs.setQueryParameter('dateFrom', dateFrom);
      handoverRecordDs.setQueryParameter('dateTo', dateTo);
      handoverRecordDs.setQueryParameter('prodLineId', prodLineId);
      handoverRecordDs.setQueryParameter('shiftTeamId', shiftTeamId);
      handoverRecordDs.setQueryParameter('shiftTeamName', shiftTeamName);
      handoverRecordDs.setQueryParameter('shiftCode', shiftCode);
      handoverRecordDs.setQueryParameter('materialName', materialName);
      handoverRecordDs.setQueryParameter('customerName', customerName);
      handoverRecordDs.query().then(res => {
        if (res && res?.success && res?.rows) {
          const dataWithNo = res?.rows?.map((item, index) => ({
            ...item,
            recordNo: index + 1,
          }));
          // 添加汇总行
          const dataWithSummary = [
            ...dataWithNo,
            {
              recordNo: '',
              prodLineCode: '',
              shiftDate: '',
              shiftCode: '',
              shiftTeamName: '',
              materialName: '',
              identification: '',
              identificationProduction: '',
              rollUpTime: '',
              transitionMeter: '',
              transitionWight: '',
              selfTest1Name: '',
              selfTest2Name: '',
              isolationMembraneTestName: '',
              isSummary: true,
            },
          ];
          handoverRecordDs.loadData(dataWithSummary);
        }
      });
    }
  }, [queryParams]);

  return (
    <div className={`${styles.tableBlock} ${styles.handoverRecord}`}>
      {/* <div className={styles.tableTitle}>交接记录表</div> */}
      <div className={styles.tableTitleLine}>
        <Button
          className={styles.powderListBtn}
          onClick={() => handleAdjustClick()}
          color={ButtonColor.default}
          disabled={!selectedRow}
        >
          交接卷调整
        </Button>
      </div>
      <div className={`${styles.tableContent} ${styles.tableContentTop}`}>
        <Table
          columns={columns}
          dataSet={handoverRecordDs}
          border
          pagination={false}
          onRow={({ record }) => ({
            onClick: () => {
              if (record.get('isSummary')) return;
              setSelectedRow(record);
            },
            className: record.get('isSummary') ? styles.summaryRow : '',
          })}
        />
      </div>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.weighingrecordreport', 'tarzan.common'],
})(
  withProps(
    () => {
      const handoverRecordDs = new DataSet({
        ...handoverRecordDS(),
      });
      return {
        handoverRecordDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(HandoverRecordTable),
);
