/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-01-16 16:00:00
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-09-25 09:22:45
 * @FilePath: \dst-front\packages\key-hmes-front\src\routes\WeighingRecordReport\components\RollInfoTable.tsx
 * @Description: 检斤记录表
 */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  Table,
  DataSet,
  Tooltip,
  Button,
  Modal,
  Form,
  TextField,
  DateTimePicker,
  Select,
  Output,
  NumberField,
  Lov,
} from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import withProps from 'utils/withProps';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import notification from 'utils/notification';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { fetchDefaultSite } from '@services/api';
import { BASIC } from '@utils/config';
import TemplatePrintButton from './TemplatePrintButton';
import { fetchAndGeneratePrintTemplate } from '../services';
import { rollInfoDS, formDS, returnAdjustDS, optionsDs, registerFormDS } from '../stores';
import styles from '../index.module.less';

interface FormData {
  eoId?: number;
  rollUpTime?: string;
  rollDownTime?: string;
  defectInformation1?: string;
  defectInformation2?: string;
  defectInformation3?: string;
  defectInformation4?: string;
  joint1?: string;
  joint2?: string;
  volumeWidth?: string;
  coilingDiameter?: string;
  selfTest1?: string;
  selfTest1Name?: string;
  selfTest2?: string;
  selfTest2Name?: string;
  isolationMembraneTest?: string;
  isolationMembraneTestName?: string;
  inspectorId?: string;
  inspectorBy?: string;
  remark?: string;
  materialLotId?: string;
  identification?: string;
  prodLineId?: string;
  shiftCode?: string;
  shiftDate?: string;
  shiftTeamId?: string;
  grossWeight?: string;
  netWeight?: string;
  shiftUsefulOutput?: string;
  shiftTeamActualId?: string;
}

const modelPrompt = 'hspc.weighingRecordReport';
const tenantId = getCurrentOrganizationId();

const RollInfoTable = ({ rollInfoDs, queryParams }) => {
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [currentSite, setCurrentSite] = useState(''); // 当前系统
  const [printStatus, setPrintStatus] = useState(false);
  const [printParams, setPrintParams] = useState<any>({});
  const [submitPrintList, setSubmitPrintList] = useState('');

  const copyNum = useRef(1); // 打印张数
  const printTemplate = useRef('');

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const returnAdjustDs = useMemo(() => new DataSet(returnAdjustDS()), []);
  const registerFormDs = useMemo(() => new DataSet(registerFormDS()), []);

  useEffect(() => {
    // 查询当前站点
    fetchDefaultSite().then(res => {
      if (res && res.success) {
        setCurrentSite(res?.rows?.siteCode);
      }
    });
  }, []);

  // 打印
  useEffect(() => {
    if (printParams?.materialLotIdList) {
      setTimeout(() => {
        // @ts-ignore
        print.current?.print();
        setSubmitPrintList('');
      }, 500);
    }
  }, [printParams]);

  const processRollInfoData = data => {
    if (!data || !data?.length) return [];

    const dateGroups = new Map<string, any[]>();
    data.forEach(item => {
      const key = item.shiftDate || '';
      if (!dateGroups.has(key)) dateGroups.set(key, []);
      dateGroups.get(key)!.push(item);
    });

    const processedData: any[] = [];

    dateGroups.forEach(itemsOfDate => {
      const teamGroups = new Map<string, any[]>();
      itemsOfDate.forEach(item => {
        const teamKey = `${item.shiftDate || ''}-${item.shiftTeamName || ''}-${item.shiftCode ||
          ''}`;
        if (!teamGroups.has(teamKey)) teamGroups.set(teamKey, []);
        teamGroups.get(teamKey)!.push(item);
      });

      teamGroups.forEach(teamItems => {
        teamItems.forEach((item, index) => {
          processedData.push({ ...item, lineNumber: index + 1 });
        });
        if (teamItems.length > 0) {
          const { shiftDate, shiftTeamName, shiftCode } = teamItems[0];
          processedData.push({
            shiftDate,
            shiftTeamName,
            shiftCode,
            isEmptyRow: true,
            className: styles.emptyRow,
          });
        }
      });
    });
    if (processedData.length > 0) {
      processedData.push({
        shiftCode: '当日生产合计',
        materialLotCode: '合格卷数',
        materialName: '待认定卷数',
        loadTime: '报废',
        unloadTime: '降级',
        splicingOrder: '异常卷',
        isGrandTotal: true,
        className: styles.grandTotal,
      });
    }

    return processedData;
  };

  const isDataRow = rec => !rec.get('isEmptyRow') && !rec.get('isGrandTotal');
  const getTeamRecords = record =>
    rollInfoDs.records.filter(
      item =>
        isDataRow(item) &&
        item.get('shiftDate') === record.get('shiftDate') &&
        item.get('shiftTeamName') === record.get('shiftTeamName') &&
        item.get('shiftCode') === record.get('shiftCode'),
    );
  const getAllRecords = () => rollInfoDs.records.filter(r => isDataRow(r));
  const sumField = (records: any[], field: string) => {
    const sum = records.reduce((total, rec) => total + (Number(rec.get(field)) || 0), 0);
    return Number(sum.toFixed(2));
  };

  const columns = [
    {
      title: '卷号信息',
      children: [
        {
          name: 'identification',
          align: ColumnAlign.center,
          width: 180,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const validSalesRollCount = recs.filter(
                rec => rec.get('identification') && rec.get('identification').trim() !== '',
              ).length;
              return (
                <span style={{ color: '#000' }}>本班生产小计 （{validSalesRollCount}卷）</span>
              );
            }
            return record?.get('identification');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'materialLotCode',
          align: ColumnAlign.center,
          width: 180,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的合格卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalPassedRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const passedRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '合格',
                ).length;
                totalPassedRollCount += passedRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  合格卷数（{totalPassedRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const passedRollCount = recs.filter(rec => rec.get('dispositionFunction') === '合格')
                .length;
              return <span style={{ color: '#000' }}>合格卷数（{passedRollCount}卷）</span>;
            }
            return record?.get('materialLotCode');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 3,
              };
            }
            return {};
          },
        },
        {
          name: 'oldVolumeNumber',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('oldVolumeNumber');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'dispositionFunction',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('dispositionFunction');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'materialName',
          align: ColumnAlign.center,
          width: 200,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalPendingRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const pendingRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '待定',
                ).length;
                totalPendingRollCount += pendingRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  待认定卷数（{totalPendingRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const pendingRollCount = recs.filter(rec => rec.get('dispositionFunction') === '待定')
                .length;
              return <span style={{ color: '#000' }}>待认定卷数（{pendingRollCount}卷）</span>;
            }
            return record?.get('materialName');
          },
        },
        {
          name: 'productionMeter',
          align: ColumnAlign.center,
          width: 70,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'productionMeter');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'productionMeter');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('productionMeter');
          },
        },
        {
          name: 'squareMeters',
          align: ColumnAlign.center,
          width: 70,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'squareMeters');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'squareMeters');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('squareMeters');
          },
        },
        {
          name: 'rollUpTime',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的报废卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalScrapRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const scrapRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '报废',
                ).length;
                totalScrapRollCount += scrapRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  报废（{totalScrapRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"报废"的记录数量
              const scrapRollCount = recs.filter(rec => rec.get('dispositionFunction') === '报废')
                .length;
              return <span style={{ color: '#000' }}>报废（{scrapRollCount}卷）</span>;
            }
            return record?.get('rollUpTime');
          },
        },
        {
          name: 'rollDownTime',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的降级卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalDowngradeRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const downgradeRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '降级',
                ).length;
                totalDowngradeRollCount += downgradeRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  降级（{totalDowngradeRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"降级"的记录数量
              const downgradeRollCount = recs.filter(
                rec => rec.get('dispositionFunction') === '降级',
              ).length;
              return <span style={{ color: '#000' }}>降级（{downgradeRollCount}卷）</span>;
            }
            return record?.get('rollDownTime');
          },
        },
        {
          name: 'volumeSequenceNum',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的异常卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalAbnormalRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const abnormalRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '异常卷',
                ).length;
                totalAbnormalRollCount += abnormalRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  异常卷（{totalAbnormalRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"异常卷"的记录数量
              const abnormalRollCount = recs.filter(
                rec => rec.get('dispositionFunction') === '异常卷',
              ).length;
              return <span style={{ color: '#000' }}>异常卷（{abnormalRollCount}卷）</span>;
            }
            return record?.get('volumeSequenceNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 2 };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '边角料',
      children: [
        {
          name: 'backgroup',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'backqty',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '杂质',
      children: [
        {
          name: 'defectInformation1',
          header: '1',
          align: ColumnAlign.center,
          width: 60,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('defectInformation1');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 9 };
            }
            return {};
          },
        },
        {
          name: 'defectInformation2',
          header: '2',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'defectInformation3',
          header: '3',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'defectInformation4',
          header: '4',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '接头',
      children: [
        {
          name: 'joint1',
          header: '1',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return <span style={{ color: '#000' }}></span>;
            }
            return record?.get('joint1');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'joint2',
          header: '2',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('joint2');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '其他信息',
      children: [
        {
          name: 'packingMethodName',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return <span style={{ color: '#000' }}></span>;
            }
            return record?.get('packingMethodName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'customerName',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('customerName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'grossWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'grossWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'grossWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('grossWeight');
          },
        },
        {
          name: 'corewEight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'corewEight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'corewEight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('corewEight');
          },
        },
        {
          name: 'isolationMembraneWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'isolationMembraneWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'isolationMembraneWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('isolationMembraneWeight');
          },
        },
        {
          name: 'aluminizingMembraneWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'aluminizingMembraneWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'aluminizingMembraneWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('aluminizingMembraneWeight');
          },
        },
        {
          name: 'netWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'netWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'netWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('netWeight');
          },
        },
        {
          name: 'detectionResults',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('detectionResults');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 12 };
            }
            return {};
          },
        },
        {
          name: 'coilingDiameter',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('coilingDiameter');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'volumeWidth',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('volumeWidth');
          },
        },
        {
          name: 'surfaceFlatness',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('surfaceFlatness');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'crossSectionFlatness',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('crossSectionFlatness');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'selfTest1Name',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('selfTest1Name');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'selfTest2Name',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('selfTest2Name');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'lotNum',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('lotNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'isolationMembraneTestName',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('isolationMembraneTestName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'inInventoryNum',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('inInventoryNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'locatorType',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('locatorType');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'netWeight',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('netWeight');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'receiveMeter',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'receiveMeter');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'receiveMeter');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('receiveMeter');
          },
        },
      ],
    },
  ];

  const groups = useMemo(
    () => [
      {
        name: 'shiftDate',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 120,
          align: 'center',
          title: '日期',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record?.get('shiftDate')} style={{ color: '#fff' }}>
                <div style={{ textAlign: 'center', width: '100%', color: '#fff' }}>
                  {record?.get('shiftDate')}
                </div>
              </Tooltip>
            );
          },
        },
      },
      {
        name: 'shiftCode',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 110,
          align: 'center',
          title: '班次',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有班组小计行的本班生产小计数量
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalValidSalesRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const validSalesRollCount = teamRecs.filter(
                  rec => rec.get('identification') && rec.get('identification').trim() !== '',
                ).length;
                totalValidSalesRollCount += validSalesRollCount;
              });
              return (
                <div
                  style={{
                    color: '#000',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  当日生产合计 （{totalValidSalesRollCount}卷）
                </div>
              );
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record?.get('shiftCode')} style={{ color: '#fff' }}>
                <div style={{ textAlign: 'center', width: '100%', color: '#fff' }}>
                  {record?.get('shiftCode')}
                </div>
              </Tooltip>
            );
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 3,
              };
            }
            return {};
          },
        },
      },
      {
        name: 'shiftTeamName',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 100,
          align: 'center',
          title: '班组',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}>班组小计</span>
            ) : (
              <Tooltip title={record?.get('shiftTeamName')} style={{ color: '#fff' }}>
                <div style={{ textAlign: 'center', width: '100%', color: '#fff' }}>
                  {record?.get('shiftTeamName')}
                </div>
              </Tooltip>
            );
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
      },
    ],
    [],
  );

  const handleRowClick = record => {
    setSelectedRow(record);
  };

  const handleGain = () => {
    const {
      corewEight,
      isolationMembraneWeight,
      aluminizingMembraneWeight,
    } = formDs.current?.toData();

    const grossWeight = formDs.current?.get('grossWeight');
    const netWeight = Number(
      Number(grossWeight) -
        Number(corewEight) -
        Number(isolationMembraneWeight) -
        Number(aluminizingMembraneWeight),
    )?.toFixed(2);

    formDs.current?.set('netWeight', netWeight);
  };

  const handlePrintException = async params => {
    setPrintStatus(false);

    const rollUpTime = await fetchAndGeneratePrintTemplate(
      '/hme-return-material-report/print/ui',
      params,
    );

    printTemplate.current = rollUpTime ? 'HME.MES.RETURN_MATERIAL_2' : 'HME.MES.RETURN_MATERIAL';
    setPrintStatus(true);
    setPrintParams(params);
  };

  // 成品调整
  const handleProductAdjustClick = () => {
    if (selectedRow) {
      const data = selectedRow?.toData?.() || {};
      optionsDs.setQueryParameter(
        'shiftTeamId',
        data.shiftTeamId || data.shiftTeam || data.shiftTeamName,
      );
      optionsDs.setQueryParameter('shiftDate', data.shiftDate);
      optionsDs.setQueryParameter('shiftCode', data.shiftCode);
      optionsDs.setQueryParameter('positionUser', 'SJZCY');
      optionsDs.query();

      formDs.loadData([data]);
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.productAdjust`).d('成品调整'),
      destroyOnClose: true,
      style: {
        width: 1200,
      },
      closable: true,
      children: (
        <div style={{ display: 'flex', alignContent: 'space-around' }}>
          <div style={{ width: '100%' }}>
            <Form dataSet={formDs} columns={2} id={styles.modalForm} className={styles.modalForm}>
              <TextField name="identification" />
              <DateTimePicker name="rollUpTime" />
              <DateTimePicker name="rollDownTime" />
              <TextField name="netWeight" restrict="0.1-9" />
              <TextField name="grossWeight" restrict="0.1-9" onChange={() => handleGain()} />
              <TextField name="defectInformation1" />
              <TextField name="defectInformation2" />
              <TextField name="defectInformation3" />
              <TextField name="defectInformation4" />
              <TextField name="joint1" />
              <TextField name="joint2" />
              <TextField name="volumeWidth" />
              <TextField name="coilingDiameter" />
              <Select name="selfTest1Obj" />
              <Select name="selfTest2Obj" />
              <Select name="isolationMembraneTestObj" />
              <Select name="inspectorByObj" />
              <TextField name="remark" />
            </Form>
          </div>
        </div>
      ),
      onCancel: () => {
        formDs.reset();
      },
      onOk: handleModalOk,
    });
  };

  // 回料调整
  const handleReturnAdjustClick = async () => {
    if (!selectedRow) {
      return;
    }
    const {
      materialLotId,
      prodLineId,
      identification,
      primaryUomQty,
      netWeight,
      grossWeight,
    } = selectedRow?.toData();
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/measurement-record/check/ui`,
      {
        method: 'GET',
        query: { materialLotId, prodLineId },
      },
    );
    if (res && res.success) {
      returnAdjustDs.loadData([
        {
          ident: identification,
          priQty: primaryUomQty,
          netWeight,
          grossWeight,
        },
      ]);
      Modal.open({
        destroyOnClose: true,
        title: '回料调整',
        style: { width: 600 },
        children: (
          <Form dataSet={returnAdjustDs} columns={2}>
            <Output name="ident" />
            <NumberField name="priQty" min={0} step={1} />
            <Output name="netWeight" />
            <Output name="grossWeight" />
          </Form>
        ),
        onOk: async () => {
          return handleReturnAdjustOk();
        },
      });
    } else {
      notification.error({
        message: res?.message,
      });
    }
  };

  // 回料调整确认
  const handleReturnAdjustOk = async () => {
    const {
      shiftTeamActualId,
      prodLineId,
      prodLineCode,
      shiftDate,
      shiftTeamCode,
      materialLotId,
    } = selectedRow?.toData();
    const payload = {
      shiftTeamActualId,
      prodLineId,
      materialLotId,
      prodLineCode,
      shiftDate,
      shiftTeamCode,
      ident: returnAdjustDs.current?.get('ident'),
      priQty: returnAdjustDs.current?.get('priQty'),
      netWeight: returnAdjustDs.current?.get('netWeight'),
      grossWeight: returnAdjustDs.current?.get('grossWeight'),
    };
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/measurement-record/adjustment/ui`,
      {
        method: 'POST',
        body: payload,
      },
    );
    if (res && res.success) {
      notification.success({
        message: intl.get('hzero.common.notification.success').d('操作成功'),
      });
      await rollInfoDs.query();
    } else {
      notification.error({
        message: res?.message,
      });
      return false;
    }
  };

  // 回料报工
  const handleReturnReportClick = () => {
    registerFormDs.reset();
    Modal.open({
      title: '回料报工',
      key: Modal.key(),
      style: {
        width: '70%',
      },
      children: (
        <Form
          className={styles.modalFormExceptionReporting}
          labelLayout={LabelLayout.horizontal}
          columns={2}
          labelWidth={140}
          dataSet={registerFormDs}
        >
          <Lov name="materialLov" />
          <Lov name="containerLov" />
          <NumberField name="tareWeight" />
          <NumberField name="netWeight" />
          <TextField name="grossWeight" restrict="0.1-9" />
        </Form>
      ),
      okText: '提交',
      onOk: async () => {
        const {
          shiftTeamActualId,
          prodLineId,
          shiftCode,
          shiftDate,
          shiftTeamId,
          locatorId,
          productionQty,
          materialId,
          shiftTeamCode,
        } = selectedRow?.toData();
        const {
          materialCode,
          containerId,
          containerCode,
          tareWeight,
          netWeight,
          grossWeight,
        } = registerFormDs.current?.toData();
        const params = {
          shiftTeamActualId,
          prodLineId,
          shiftCode,
          shiftDate,
          shiftTeamId,
          shiftTeamCode,
          materialId,
          locatorId,
          code: materialCode,
          priQty: productionQty,
          netWeight,
          grossWeight,
          tareWeight,
          containerId,
          containerCode,
        };
        const res = await request(
          `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/measurement-record/report/ui`,
          {
            method: 'POST',
            body: params,
          },
        );
        if (res && res?.success) {
          await rollInfoDs.query();
          if (res?.rows[0]) {
            // 根据站点确认打印条数
            switch (currentSite) {
              case 'DST':
                copyNum.current = 2;
                break;
              default:
                copyNum.current = 1;
                break;
            }
            // 打印这条数据
            handlePrintException({
              materialLotIdList: res?.content[0]?.rows.join(','),
              shiftTeamId: selectedRow?.shiftTeamId,
            });
            setSubmitPrintList(res?.content[0]?.rows.join(','));
          }
        } else {
          notification.error({
            message: res?.message,
          });
          return false;
        }
      },
    });
  };

  const handleModalOk = async () => {
    const formData = (formDs.toData()?.[0] || {}) as FormData;

    const params = {
      eoId: formData?.eoId,
      rollUpTime: formData?.rollUpTime,
      rollDownTime: formData?.rollDownTime,
      defectInformation1: formData?.defectInformation1 || 0,
      defectInformation2: formData?.defectInformation2 || 0,
      defectInformation3: formData?.defectInformation3 || 0,
      defectInformation4: formData?.defectInformation4 || 0,
      joint1: formData?.joint1 || 0,
      joint2: formData?.joint2 || 0,
      volumeWidth: formData?.volumeWidth,
      coilingDiameter: formData?.coilingDiameter,
      selfTest1: formData?.selfTest1,
      selfTest1Name: formData?.selfTest1Name,
      selfTest2: formData?.selfTest2,
      selfTest2Name: formData?.selfTest2Name,
      isolationMembraneTest: formData?.isolationMembraneTest,
      isolationMembraneTestName: formData?.isolationMembraneTestName,
      inspectorId: formData?.inspectorId,
      inspectorBy: formData?.inspectorBy,
      remark: formData?.remark,
      materialLotId: formData?.materialLotId,
      identification: formData?.identification,
      shiftTeamActualId: formData?.shiftTeamActualId,
      prodLineId: formData?.prodLineId,
      shiftCode: formData?.shiftCode,
      shiftDate: formData?.shiftDate,
      shiftTeamId: formData?.shiftTeamId,
      totalOutput: formData?.netWeight,
      shiftUsefulOutput: formData?.grossWeight,
    };

    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/finished-product/adjustment/ui`,
      {
        method: 'POST',
        body: params,
      },
    );
    if (res && res?.success) {
      notification.success({
        message: intl.get('hzero.common.notification.success').d('操作成功'),
      });
      await rollInfoDs.query();
      return true;
    }
    notification.error({
      message: res?.message || '操作失败',
    });
    return false;
  };

  useEffect(() => {
    if (queryParams && Object.keys(queryParams).length > 0) {
      const {
        dateFrom,
        dateTo,
        prodLineId,
        shiftTeamId,
        shiftTeamName,
        shiftCode,
        materialName,
        customerName,
      } = queryParams;
      rollInfoDs.setQueryParameter('dateFrom', dateFrom);
      rollInfoDs.setQueryParameter('dateTo', dateTo);
      rollInfoDs.setQueryParameter('prodLineId', prodLineId);
      rollInfoDs.setQueryParameter('shiftTeamId', shiftTeamId);
      rollInfoDs.setQueryParameter('shiftTeamName', shiftTeamName);
      rollInfoDs.setQueryParameter('shiftCode', shiftCode);
      rollInfoDs.setQueryParameter('materialName', materialName);
      rollInfoDs.setQueryParameter('customerName', customerName);
      rollInfoDs.query().then(res => {
        if (res && res?.success && res?.rows) {
          const processedData = processRollInfoData(res?.rows);
          rollInfoDs.loadData(processedData);
        }
      });
    }
  }, [queryParams]);

  return (
    <div className={`${styles.tableBlock} ${styles.rollInfo}`}>
      {/* <div className={styles.tableTitle}>检斤记录表</div> */}
      <div className={styles.tableTitleLine}>
        <Button
          className={styles.powderListBtn}
          onClick={handleProductAdjustClick}
          color={ButtonColor.default}
          disabled={!selectedRow || selectedRow.get('backFlag') === 'Y'}
        >
          成品调整
        </Button>
        <Button
          className={styles.powderListBtn}
          onClick={handleReturnAdjustClick}
          color={ButtonColor.default}
          disabled={!selectedRow || selectedRow.get('backFlag') === 'N'}
        >
          回料调整
        </Button>
        <Button
          className={styles.powderListBtn}
          onClick={handleReturnReportClick}
          color={ButtonColor.default}
          disabled={!selectedRow || selectedRow.get('backFlag') === 'N'}
        >
          回料报工
        </Button>
      </div>
      <div style={{ display: 'none' }}>
        {printStatus && (
          <TemplatePrintButton
            ref={print}
            printButtonCode={printTemplate.current}
            disabled={!selectedRow}
            name={intl.get(`${modelPrompt}.button.print`).d('重新打印')}
            printParams={{
              materialLotIdList: selectedRow?.materialLotId || submitPrintList,
              shiftTeamId: selectedRow?.shiftTeamId,
              copy: copyNum.current,
            }}
            printCallback={() => {
              copyNum.current = 1;
            }}
            icon=""
            frUrl="/hme-return-material-report/print/ui"
          />
        )}
      </div>
      <div className={`${styles.tableContent} ${styles.tableContentTop}`}>
        <Table
          columns={columns}
          dataSet={rollInfoDs}
          border
          pagination={false}
          groups={groups as any}
          style={{ maxHeight: 500 }}
          onRow={({ record }) => ({
            onClick: () => {
              if (record.get('isEmptyRow')) {
                return;
              }
              handleRowClick(record);
            },
            className:
              record.get('isSummary') || record.get('className')
                ? record.get('isSummary')
                  ? styles.summaryRow
                  : record.get('className')
                : '',
          })}
        />
      </div>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.weighingrecordreport', 'tarzan.common'],
})(
  withProps(
    () => {
      const rollInfoDs = new DataSet({
        ...rollInfoDS(),
      });
      return {
        rollInfoDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RollInfoTable),
);
